# دليل استخدام مكونات Toolbar

## 📋 المحتويات

- [مقدمة](#مقدمة)
- [المفاهيم الأساسية](#المفاهيم-الأساسية)
- [المكون الرئيسي Toolbar](#المكون-الرئيسي-toolbar)
- [مكون البحث ToolbarSearch](#مكون-البحث-toolbarsearch)
- [مكون التصفية ToolbarFilter](#مكون-التصفية-toolbarfilter)
- [مكون الترتيب ToolbarSorting](#مكون-الترتيب-toolbarsorting)
- [أمثلة عملية شاملة](#أمثلة-عملية-شاملة)
- [استكشاف الأخطاء والحلول](#استكشاف-الأخطاء-والحلول)

---

## 🚀 مقدمة

مكونات **Toolbar** هي مجموعة من المكونات المتكاملة التي تسمح لك بإنشاء شريط أدوات تفاعلي لجداول البيانات. هذه المكونات تدعم:

### ✨ الميزات الرئيسية

- **🔍 البحث المتقدم**: بحث فوري مع تأخير ذكي (debouncing)
- **🎯 التصفية المرنة**: دعم أنواع متعددة من الفلاتر (نص، رقم، تاريخ، اختيار)
- **📊 الترتيب المتعدد**: ترتيب متعدد المستويات مع إمكانية السحب والإفلات
- **🌐 دعم متعدد اللغات**: العربية والإنجليزية مع دعم RTL/LTR
- **🔗 إدارة حالة URL**: حفظ الحالة في رابط الصفحة تلقائياً
- **⚡ الأداء المحسن**: استخدام React Transitions للتحديثات السلسة

### 🎯 متى تستخدم هذه المكونات؟

- عندما تحتاج لإضافة وظائف بحث وتصفية لجدول بيانات
- عندما تريد حفظ حالة البحث والتصفية في رابط الصفحة
- عندما تحتاج واجهة مستخدم متجاوبة ومتعددة اللغات
- عندما تريد تجربة مستخدم سلسة مع تحديثات فورية

---

## 🧠 المفاهيم الأساسية

### 1. **السياق (Context)**

جميع المكونات تعمل داخل `ToolbarContext` الذي يوفر:

- إعدادات اللغة والاتجاه
- قواميس الترجمة
- دالة `startTransition` للتحديثات السلسة

### 2. **إدارة حالة URL**

- استخدام مكتبة `nuqs` لحفظ الحالة في URL
- دعم `shallow routing` لتجنب إعادة تحميل الصفحة
- تحديث تلقائي للمعاملات في رابط الصفحة

### 3. **التأخير الذكي (Debouncing)**

- تقليل عدد التحديثات أثناء الكتابة
- قابل للتخصيص لكل مكون
- افتراضي: 300 مللي ثانية

### 4. **التحقق من صحة البيانات**

- استخدام مكتبة `zod` للتحقق من صحة البيانات
- أنواع TypeScript قوية لضمان الأمان
- معالجة أخطاء تلقائية

---

## 🏗️ المكون الرئيسي Toolbar

المكون `Toolbar` هو الحاوي الرئيسي الذي يوفر السياق والإعدادات لجميع المكونات الفرعية.

### 📝 الخصائص (Props)

```typescript
interface ToolbarProps {
  children?: React.ReactNode
  className?: string
  locale?: "ar" | "en" // افتراضي: "ar"
  dir?: "rtl" | "ltr" // افتراضي: "rtl"
  startTransition?: React.TransitionStartFunction
}
```

### 📋 شرح الخصائص

| الخاصية           | النوع                     | الافتراضي | الوصف                                      |
| ----------------- | ------------------------- | --------- | ------------------------------------------ |
| `children`        | `ReactNode`               | -         | المكونات الفرعية (البحث، التصفية، الترتيب) |
| `className`       | `string`                  | -         | فئات CSS إضافية للحاوي                     |
| `locale`          | `"ar" \| "en"`            | `"ar"`    | لغة الواجهة                                |
| `dir`             | `"rtl" \| "ltr"`          | `"rtl"`   | اتجاه النص                                 |
| `startTransition` | `TransitionStartFunction` | -         | دالة React Transition للتحديثات السلسة     |

### 🔧 الاستخدام الأساسي

```tsx
import { useTransition } from "react"

import { Toolbar } from "@/components/toolbar"

function MyComponent() {
  const [isPending, startTransition] = useTransition()

  return (
    <Toolbar startTransition={startTransition} locale="ar" dir="rtl">
      {/* المكونات الفرعية هنا */}
    </Toolbar>
  )
}
```

### 🌐 دعم متعدد اللغات

```tsx
// للعربية (افتراضي)
<Toolbar locale="ar" dir="rtl">
  {/* المحتوى */}
</Toolbar>

// للإنجليزية
<Toolbar locale="en" dir="ltr">
  {/* المحتوى */}
</Toolbar>
```

### ⚡ استخدام React Transitions

```tsx
import { useTransition } from "react"

function DataTableWithToolbar() {
  const [isPending, startTransition] = useTransition()

  return (
    <div>
      <Toolbar startTransition={startTransition}>{/* مكونات الشريط */}</Toolbar>

      {/* الجدول مع مؤشر التحميل */}
      <div className={isPending ? "opacity-50" : ""}>
        <DataTable />
      </div>
    </div>
  )
}
```

### 🎨 التخصيص والتنسيق

```tsx
<Toolbar
  className="bg-background flex gap-2 rounded-lg border p-4"
  startTransition={startTransition}
>
  {/* المكونات الفرعية */}
</Toolbar>
```

### ⚠️ ملاحظات مهمة

1. **السياق المطلوب**: جميع المكونات الفرعية يجب أن تكون داخل `Toolbar`
2. **React Transitions**: استخدم `startTransition` لتحسين الأداء
3. **اللغة والاتجاه**: تأكد من تطابق `locale` و `dir`
4. **الترتيب**: ضع `Toolbar` قبل الجدول مباشرة

---

## 🔍 مكون البحث ToolbarSearch

مكون `ToolbarSearch` يوفر حقل بحث تفاعلي مع تأخير ذكي وحفظ الحالة في URL.

### 📝 الخصائص (Props)

```typescript
interface SearchProps<T> {
  item: {
    id: Extract<keyof T, string>
    placeholder?: string
  }
  shallow?: boolean // افتراضي: false
  debounceMs?: number // افتراضي: 300
  className?: string
}
```

### 📋 شرح الخصائص

| الخاصية            | النوع     | الافتراضي | الوصف                             |
| ------------------ | --------- | --------- | --------------------------------- |
| `item.id`          | `string`  | **مطلوب** | معرف الحقل المراد البحث فيه       |
| `item.placeholder` | `string`  | -         | النص التوضيحي في حقل البحث        |
| `shallow`          | `boolean` | `false`   | تحديث URL بدون إعادة تحميل الصفحة |
| `debounceMs`       | `number`  | `300`     | مدة التأخير بالمللي ثانية         |
| `className`        | `string`  | -         | فئات CSS إضافية                   |

### 🔧 الاستخدام الأساسي

```tsx
import { ToolbarSearch } from "@/components/toolbar"

// بحث بسيط

;<ToolbarSearch
  item={{
    id: "name",
    placeholder: "بحث بالاسم...",
  }}
/>
```

### 🎯 أمثلة متقدمة

#### 1. بحث مع تخصيص التأخير

```tsx
<ToolbarSearch
  item={{
    id: "email",
    placeholder: "بحث بالبريد الإلكتروني...",
  }}
  debounceMs={500} // تأخير أطول للبحث المعقد
/>
```

#### 2. بحث مع Shallow Routing

```tsx
<ToolbarSearch
  item={{
    id: "title",
    placeholder: "بحث بالعنوان...",
  }}
  shallow={true} // لا يعيد تحميل الصفحة
/>
```

#### 3. بحث مع تنسيق مخصص

```tsx
<ToolbarSearch
  item={{
    id: "description",
    placeholder: "بحث في الوصف...",
  }}
  className="w-full max-w-sm"
/>
```

### 🔗 إدارة حالة URL

المكون يحفظ حالة البحث في URL تلقائياً:

```
# قبل البحث
/page

# بعد البحث بكلمة "أحمد"
/page?search={"id":"name","value":"أحمد"}
```

### 📊 استخدام مع TypeScript

```tsx
interface User {
  id: string
  name: string
  email: string
}

// البحث في حقل الاسم
;<ToolbarSearch<User>
  item={{
    id: "name", // TypeScript يتحقق من وجود هذا الحقل
    placeholder: "بحث بالاسم...",
  }}
/>
```

### ⚡ الأداء والتحسين

```tsx
import { useTransition } from "react"

function OptimizedSearch() {
  const [isPending, startTransition] = useTransition()

  return (
    <Toolbar startTransition={startTransition}>
      <ToolbarSearch
        item={{ id: "name", placeholder: "بحث..." }}
        debounceMs={300} // توازن بين الاستجابة والأداء
      />
    </Toolbar>
  )
}
```

### 🎨 التخصيص المتقدم

```tsx
// تخصيص شكل حقل البحث
<ToolbarSearch
  item={{ id: "name", placeholder: "بحث متقدم..." }}
  className="[&_input]:bg-muted/50 [&_input]:border-primary/20 [&_input]:focus:border-primary w-full max-w-md"
/>
```

### ⚠️ ملاحظات مهمة

1. **السياق المطلوب**: يجب استخدامه داخل `Toolbar`
2. **معرف فريد**: `item.id` يجب أن يكون فريد لكل بحث
3. **التأخير الذكي**: يقلل من عدد الطلبات أثناء الكتابة
4. **حفظ الحالة**: البحث محفوظ في URL ويمكن مشاركته

---

## 🎯 مكون التصفية ToolbarFilter

مكون `ToolbarFilter` هو الأكثر تعقيداً ومرونة، يدعم أنواع متعددة من الفلاتر مع واجهة سهلة الاستخدام.

### 📝 الخصائص (Props)

```typescript
interface ToolbarFilterProps<T> {
  items: FilterItem<T>[]
  shallow?: boolean // افتراضي: false
  debounceMs?: number // افتراضي: 300
  className?: string
}
```

### 🎨 أنواع الفلاتر المدعومة

| النوع         | الوصف        | العمليات المدعومة                                |
| ------------- | ------------ | ------------------------------------------------ |
| `text`        | نص           | يحتوي، لا يحتوي، يساوي، لا يساوي، فارغ، غير فارغ |
| `number`      | رقم          | يساوي، أكبر من، أصغر من، بين، فارغ، غير فارغ     |
| `date`        | تاريخ        | يساوي، قبل، بعد، بين، فارغ، غير فارغ             |
| `boolean`     | منطقي        | صحيح، خطأ                                        |
| `select`      | اختيار واحد  | يساوي، لا يساوي، فارغ، غير فارغ                  |
| `multiSelect` | اختيار متعدد | يحتوي على، لا يحتوي على، فارغ، غير فارغ          |

### 🔧 الاستخدام الأساسي

```tsx
import { ToolbarFilter } from "@/components/toolbar"

;<ToolbarFilter
  items={[
    {
      variant: "text",
      id: "name",
      label: "الاسم",
      placeholder: "أدخل الاسم...",
    },
  ]}
/>
```

### 📋 أمثلة لكل نوع فلتر

#### 1. فلتر النص (Text)

```tsx
<ToolbarFilter
  items={[
    {
      variant: "text",
      id: "name",
      label: "الاسم",
      placeholder: "بحث بالاسم...",
    },
    {
      variant: "text",
      id: "email",
      label: "البريد الإلكتروني",
      placeholder: "أدخل البريد...",
    },
  ]}
/>
```

#### 2. فلتر الأرقام (Number)

```tsx
<ToolbarFilter
  items={[
    {
      variant: "number",
      id: "age",
      label: "العمر",
      placeholder: "أدخل العمر...",
    },
    {
      variant: "number",
      id: "salary",
      label: "الراتب",
      placeholder: "أدخل الراتب...",
    },
  ]}
/>
```

#### 3. فلتر التاريخ (Date)

```tsx
<ToolbarFilter
  items={[
    {
      variant: "date",
      id: "createdAt",
      label: "تاريخ الإنشاء",
    },
    {
      variant: "date",
      id: "updatedAt",
      label: "تاريخ التحديث",
    },
  ]}
/>
```

#### 4. فلتر منطقي (Boolean)

```tsx
<ToolbarFilter
  items={[
    {
      variant: "boolean",
      id: "isActive",
      label: "نشط",
    },
    {
      variant: "boolean",
      id: "isVerified",
      label: "مُتحقق منه",
    },
  ]}
/>
```

#### 5. فلتر الاختيار الواحد (Select)

```tsx
<ToolbarFilter
  items={[
    {
      variant: "select",
      id: "status",
      label: "الحالة",
      options: [
        { label: "نشط", value: "active" },
        { label: "غير نشط", value: "inactive" },
        { label: "معلق", value: "pending" },
      ],
    },
  ]}
/>
```

#### 6. فلتر الاختيار المتعدد (MultiSelect)

```tsx
<ToolbarFilter
  items={[
    {
      variant: "multiSelect",
      id: "categories",
      label: "الفئات",
      options: [
        {
          label: "تقنية",
          value: "tech",
          icon: Laptop,
          count: 25,
        },
        {
          label: "تصميم",
          value: "design",
          icon: Palette,
          count: 12,
        },
        {
          label: "تسويق",
          value: "marketing",
          icon: Megaphone,
          count: 8,
        },
      ],
    },
  ]}
/>
```

### 🔗 ربط الفلاتر (Filter Joining)

يمكن ربط الفلاتر باستخدام `AND` أو `OR`:

```tsx
// سيظهر زر لتغيير نوع الربط
<ToolbarFilter
  items={[
    { variant: "text", id: "name", label: "الاسم" },
    { variant: "text", id: "email", label: "البريد" },
  ]}
/>
```

### 🎛️ العمليات المتاحة

#### عمليات النص:

- `iLike`: يحتوي على
- `notILike`: لا يحتوي على
- `eq`: يساوي
- `ne`: لا يساوي
- `isEmpty`: فارغ
- `isNotEmpty`: غير فارغ

#### عمليات الأرقام والتواريخ:

- `eq`: يساوي
- `ne`: لا يساوي
- `lt`: أصغر من
- `lte`: أصغر من أو يساوي
- `gt`: أكبر من
- `gte`: أكبر من أو يساوي
- `isBetween`: بين قيمتين
- `isEmpty`: فارغ
- `isNotEmpty`: غير فارغ

#### عمليات الاختيار:

- `eq`: يساوي (للاختيار الواحد)
- `ne`: لا يساوي (للاختيار الواحد)
- `inArray`: يحتوي على (للاختيار المتعدد)
- `notInArray`: لا يحتوي على (للاختيار المتعدد)

### 🔧 مثال شامل

```tsx
interface Product {
  id: string
  name: string
  price: number
  category: string
  isAvailable: boolean
  createdAt: Date
}

;<ToolbarFilter<Product>
  items={[
    {
      variant: "text",
      id: "name",
      label: "اسم المنتج",
      placeholder: "بحث بالاسم...",
    },
    {
      variant: "number",
      id: "price",
      label: "السعر",
      placeholder: "أدخل السعر...",
    },
    {
      variant: "select",
      id: "category",
      label: "الفئة",
      options: [
        { label: "إلكترونيات", value: "electronics" },
        { label: "ملابس", value: "clothing" },
        { label: "كتب", value: "books" },
      ],
    },
    {
      variant: "boolean",
      id: "isAvailable",
      label: "متوفر",
    },
    {
      variant: "date",
      id: "createdAt",
      label: "تاريخ الإضافة",
    },
  ]}
  debounceMs={500}
  shallow={false}
/>
```

### 🔗 إدارة حالة URL

الفلاتر تُحفظ في URL بصيغة JSON:

```
# مثال على URL مع فلاتر متعددة
/page?filters=[
  {"filterId":"f1","variant":"text","id":"name","operator":"iLike","value":"أحمد"},
  {"filterId":"f2","variant":"number","id":"age","operator":"gte","value":"25"}
]&join=AND
```

### 🎨 التخصيص المتقدم

```tsx
// تخصيص شكل زر الفلتر
<ToolbarFilter
  items={filterItems}
  className="[&_button]:bg-primary/10 [&_button]:border-primary/20 [&_button:hover]:bg-primary/20"
/>
```

### ⚡ نصائح للأداء

1. **استخدم debounceMs مناسب**:

```tsx
<ToolbarFilter
  items={items}
  debounceMs={500} // للبحث في قواعد بيانات كبيرة
/>
```

2. **استخدم shallow routing للتحديثات السريعة**:

```tsx
<ToolbarFilter
  items={items}
  shallow={true} // لا يعيد تحميل الصفحة
/>
```

3. **حدد الخيارات بعناية**:

```tsx
// أضف count للخيارات الشائعة
{
  variant: "select",
  id: "status",
  label: "الحالة",
  options: [
    { label: "نشط", value: "active", count: 150 },
    { label: "غير نشط", value: "inactive", count: 25 }
  ]
}
```

### ⚠️ ملاحظات مهمة

1. **السياق المطلوب**: يجب استخدامه داخل `Toolbar`
2. **معرفات فريدة**: كل `id` يجب أن يكون فريد
3. **أنواع البيانات**: تأكد من تطابق `variant` مع نوع البيانات
4. **الخيارات**: للـ select و multiSelect، يجب توفير `options`
5. **الأداء**: استخدم debouncing مناسب لحجم البيانات

---

## 📊 مكون الترتيب ToolbarSorting

مكون `ToolbarSorting` يوفر ترتيب متعدد المستويات مع إمكانية السحب والإفلات لإعادة ترتيب الأولويات.

### 📝 الخصائص (Props)

```typescript
interface ToolbarSortingProps<T> {
  items: SortingItem<T>[]
  className?: string
}

interface SortingItem<T> {
  id: Extract<keyof T, string>
  label: string
}
```

### 📋 شرح الخصائص

| الخاصية         | النوع           | الوصف                                    |
| --------------- | --------------- | ---------------------------------------- |
| `items`         | `SortingItem[]` | **مطلوب** - قائمة الحقول القابلة للترتيب |
| `items[].id`    | `string`        | معرف الحقل                               |
| `items[].label` | `string`        | النص المعروض للمستخدم                    |
| `className`     | `string`        | فئات CSS إضافية                          |

### 🔧 الاستخدام الأساسي

```tsx
import { ToolbarSorting } from "@/components/toolbar"

;<ToolbarSorting
  items={[
    { id: "name", label: "الاسم" },
    { id: "createdAt", label: "تاريخ الإنشاء" },
    { id: "updatedAt", label: "تاريخ التحديث" },
  ]}
/>
```

### 🎯 ميزات الترتيب

#### 1. **ترتيب متعدد المستويات**

- يمكن إضافة عدة مستويات ترتيب
- كل مستوى له اتجاه منفصل (تصاعدي/تنازلي)
- الأولوية حسب الترتيب في القائمة

#### 2. **السحب والإفلات**

- إعادة ترتيب الأولويات بالسحب
- واجهة بصرية واضحة
- تحديث فوري للترتيب

#### 3. **اتجاهات الترتيب**

- `asc`: تصاعدي (A-Z, 1-9, قديم-جديد)
- `desc`: تنازلي (Z-A, 9-1, جديد-قديم)

### 📊 أمثلة عملية

#### 1. ترتيب بسيط

```tsx
interface User {
  id: string
  name: string
  email: string
  createdAt: Date
}

;<ToolbarSorting<User>
  items={[
    { id: "name", label: "الاسم" },
    { id: "email", label: "البريد الإلكتروني" },
    { id: "createdAt", label: "تاريخ التسجيل" },
  ]}
/>
```

#### 2. ترتيب للمنتجات

```tsx
interface Product {
  id: string
  name: string
  price: number
  rating: number
  salesCount: number
  createdAt: Date
}

;<ToolbarSorting<Product>
  items={[
    { id: "name", label: "اسم المنتج" },
    { id: "price", label: "السعر" },
    { id: "rating", label: "التقييم" },
    { id: "salesCount", label: "عدد المبيعات" },
    { id: "createdAt", label: "تاريخ الإضافة" },
  ]}
/>
```

#### 3. ترتيب مع تنسيق مخصص

```tsx
<ToolbarSorting
  items={[
    { id: "priority", label: "الأولوية" },
    { id: "status", label: "الحالة" },
    { id: "dueDate", label: "تاريخ الاستحقاق" },
  ]}
  className="[&_button]:bg-secondary/50"
/>
```

### 🔗 إدارة حالة URL

الترتيب يُحفظ في URL كمصفوفة:

```
# مثال على URL مع ترتيب متعدد
/page?sort=[
  {"id":"name","value":"asc"},
  {"id":"createdAt","value":"desc"}
]
```

### 🎨 واجهة المستخدم

#### عرض الترتيب الحالي:

- **زر الترتيب**: يظهر عدد مستويات الترتيب النشطة
- **قائمة الترتيب**: تظهر جميع المستويات مع إمكانية التعديل
- **مؤشرات بصرية**: أسهم تشير لاتجاه الترتيب

#### التفاعل:

- **إضافة ترتيب**: زر "إضافة ترتيب"
- **تغيير الاتجاه**: نقر على السهم
- **إعادة الترتيب**: سحب وإفلات
- **حذف مستوى**: زر الحذف لكل مستوى
- **إعادة تعيين**: زر "إعادة تعيين الترتيب"

### ⚡ سيناريوهات الاستخدام

#### 1. ترتيب جدول المستخدمين

```tsx
// ترتيب أولاً بالحالة، ثم بالاسم
// النتيجة: المستخدمون النشطون أولاً، مرتبون أبجدياً
<ToolbarSorting<User>
  items={[
    { id: "status", label: "الحالة" },
    { id: "name", label: "الاسم" },
    { id: "lastLogin", label: "آخر دخول" },
  ]}
/>
```

#### 2. ترتيب قائمة المهام

```tsx
// ترتيب بالأولوية ثم بتاريخ الاستحقاق
<ToolbarSorting<Task>
  items={[
    { id: "priority", label: "الأولوية" },
    { id: "dueDate", label: "تاريخ الاستحقاق" },
    { id: "status", label: "الحالة" },
  ]}
/>
```

#### 3. ترتيب المبيعات

```tsx
// ترتيب بالمبلغ ثم بالتاريخ
<ToolbarSorting<Sale>
  items={[
    { id: "amount", label: "المبلغ" },
    { id: "date", label: "التاريخ" },
    { id: "customer", label: "العميل" },
  ]}
/>
```

### 🎛️ التحكم البرمجي

```tsx
import { useQueryState } from "nuqs"

import { getSortingStateParser } from "@/components/toolbar/lib"

function CustomSortingControl() {
  const [sorting, setSorting] = useQueryState(
    "sort",
    getSortingStateParser(["name", "date"]).withDefault([])
  )

  // إضافة ترتيب برمجياً
  const addSort = () => {
    setSorting([...sorting, { id: "name", value: "asc" }])
  }

  // مسح جميع الترتيبات
  const clearSort = () => {
    setSorting([])
  }

  return (
    <div>
      <ToolbarSorting items={items} />
      <button onClick={addSort}>إضافة ترتيب</button>
      <button onClick={clearSort}>مسح الترتيب</button>
    </div>
  )
}
```

### 🎨 التخصيص المتقدم

```tsx
// تخصيص شكل زر الترتيب
<ToolbarSorting
  items={items}
  className="[&_button:hover]:from-blue-600 [&_button:hover]:to-purple-600 [&_button]:bg-gradient-to-r [&_button]:from-blue-500 [&_button]:to-purple-500 [&_button]:text-white"
/>
```

### ⚠️ ملاحظات مهمة

1. **السياق المطلوب**: يجب استخدامه داخل `Toolbar`
2. **معرفات صحيحة**: تأكد أن `id` يطابق حقول البيانات
3. **ترتيب الأولوية**: الترتيب الأول له الأولوية الأعلى
4. **الأداء**: الترتيب يحدث على مستوى قاعدة البيانات
5. **حفظ الحالة**: الترتيب محفوظ في URL ويمكن مشاركته

### 🔄 دورة حياة الترتيب

1. **المستخدم ينقر على "ترتيب"**
2. **يختار حقل للترتيب**
3. **يختار الاتجاه (تصاعدي/تنازلي)**
4. **يمكن إضافة مستويات إضافية**
5. **يمكن إعادة ترتيب الأولويات بالسحب**
6. **التحديث فوري في URL والجدول**

---

## 🚀 أمثلة عملية شاملة

### 📊 مثال 1: نظام إدارة المستخدمين

```tsx
import { useTransition } from "react"

import {
  Toolbar,
  ToolbarFilter,
  ToolbarSearch,
  ToolbarSorting,
} from "@/components/toolbar"

interface User {
  id: string
  name: string
  email: string
  role: "admin" | "user" | "moderator"
  isActive: boolean
  createdAt: Date
  lastLogin: Date
}

function UsersManagement() {
  const [isPending, startTransition] = useTransition()

  return (
    <div className="space-y-4">
      <Toolbar startTransition={startTransition}>
        {/* البحث */}
        <ToolbarSearch<User>
          item={{
            id: "name",
            placeholder: "بحث بالاسم أو البريد الإلكتروني...",
          }}
        />

        {/* التصفية */}
        <ToolbarFilter<User>
          items={[
            {
              variant: "text",
              id: "name",
              label: "الاسم",
              placeholder: "أدخل الاسم...",
            },
            {
              variant: "text",
              id: "email",
              label: "البريد الإلكتروني",
              placeholder: "أدخل البريد...",
            },
            {
              variant: "select",
              id: "role",
              label: "الدور",
              options: [
                { label: "مدير", value: "admin" },
                { label: "مستخدم", value: "user" },
                { label: "مشرف", value: "moderator" },
              ],
            },
            {
              variant: "boolean",
              id: "isActive",
              label: "نشط",
            },
            {
              variant: "date",
              id: "createdAt",
              label: "تاريخ التسجيل",
            },
          ]}
        />

        {/* الترتيب */}
        <ToolbarSorting<User>
          items={[
            { id: "name", label: "الاسم" },
            { id: "email", label: "البريد الإلكتروني" },
            { id: "role", label: "الدور" },
            { id: "createdAt", label: "تاريخ التسجيل" },
            { id: "lastLogin", label: "آخر دخول" },
          ]}
        />
      </Toolbar>

      {/* الجدول */}
      <div className={isPending ? "opacity-50" : ""}>
        <UsersTable />
      </div>
    </div>
  )
}
```

### 🛍️ مثال 2: متجر إلكتروني

```tsx
interface Product {
  id: string
  name: string
  description: string
  price: number
  category: string
  brand: string
  rating: number
  inStock: boolean
  tags: string[]
  createdAt: Date
}

function ProductsCatalog() {
  const [isPending, startTransition] = useTransition()

  return (
    <div className="space-y-4">
      <Toolbar
        startTransition={startTransition}
        className="bg-card flex flex-wrap gap-2 rounded-lg border p-4"
      >
        {/* البحث في المنتجات */}
        <ToolbarSearch<Product>
          item={{
            id: "name",
            placeholder: "بحث في المنتجات...",
          }}
          className="min-w-[200px] flex-1"
        />

        {/* فلاتر متقدمة */}
        <ToolbarFilter<Product>
          items={[
            {
              variant: "text",
              id: "name",
              label: "اسم المنتج",
              placeholder: "أدخل اسم المنتج...",
            },
            {
              variant: "number",
              id: "price",
              label: "السعر",
              placeholder: "أدخل السعر...",
            },
            {
              variant: "select",
              id: "category",
              label: "الفئة",
              options: [
                { label: "إلكترونيات", value: "electronics", count: 150 },
                { label: "ملابس", value: "clothing", count: 89 },
                { label: "كتب", value: "books", count: 45 },
                { label: "رياضة", value: "sports", count: 67 },
              ],
            },
            {
              variant: "multiSelect",
              id: "tags",
              label: "العلامات",
              options: [
                { label: "جديد", value: "new", count: 25 },
                { label: "مخفض", value: "sale", count: 12 },
                { label: "شائع", value: "popular", count: 8 },
                { label: "محدود", value: "limited", count: 3 },
              ],
            },
            {
              variant: "boolean",
              id: "inStock",
              label: "متوفر",
            },
            {
              variant: "number",
              id: "rating",
              label: "التقييم",
              placeholder: "من 1 إلى 5...",
            },
          ]}
          debounceMs={400}
        />

        {/* ترتيب المنتجات */}
        <ToolbarSorting<Product>
          items={[
            { id: "name", label: "الاسم" },
            { id: "price", label: "السعر" },
            { id: "rating", label: "التقييم" },
            { id: "createdAt", label: "الأحدث" },
          ]}
        />
      </Toolbar>

      {/* شبكة المنتجات */}
      <div className={isPending ? "opacity-50 transition-opacity" : ""}>
        <ProductsGrid />
      </div>
    </div>
  )
}
```

### 📋 مثال 3: نظام إدارة المهام

```tsx
interface Task {
  id: string
  title: string
  description: string
  priority: "low" | "medium" | "high" | "urgent"
  status: "todo" | "in_progress" | "review" | "done"
  assignee: string
  tags: string[]
  dueDate: Date
  createdAt: Date
}

function TasksManagement() {
  const [isPending, startTransition] = useTransition()

  return (
    <div className="space-y-4">
      <Toolbar
        startTransition={startTransition}
        className="bg-muted/50 grid grid-cols-1 gap-4 rounded-lg p-4 md:grid-cols-3"
      >
        {/* البحث في المهام */}
        <ToolbarSearch<Task>
          item={{
            id: "title",
            placeholder: "بحث في المهام...",
          }}
          className="md:col-span-3"
        />

        {/* فلاتر المهام */}
        <div className="md:col-span-2">
          <ToolbarFilter<Task>
            items={[
              {
                variant: "select",
                id: "priority",
                label: "الأولوية",
                options: [
                  { label: "عاجل", value: "urgent", count: 5 },
                  { label: "عالي", value: "high", count: 12 },
                  { label: "متوسط", value: "medium", count: 25 },
                  { label: "منخفض", value: "low", count: 8 },
                ],
              },
              {
                variant: "select",
                id: "status",
                label: "الحالة",
                options: [
                  { label: "قيد الانتظار", value: "todo", count: 15 },
                  { label: "قيد التنفيذ", value: "in_progress", count: 8 },
                  { label: "قيد المراجعة", value: "review", count: 4 },
                  { label: "مكتمل", value: "done", count: 23 },
                ],
              },
              {
                variant: "text",
                id: "assignee",
                label: "المكلف",
                placeholder: "اسم المكلف...",
              },
              {
                variant: "multiSelect",
                id: "tags",
                label: "العلامات",
                options: [
                  { label: "عاجل", value: "urgent" },
                  { label: "خطأ", value: "bug" },
                  { label: "ميزة", value: "feature" },
                  { label: "تحسين", value: "enhancement" },
                ],
              },
              {
                variant: "date",
                id: "dueDate",
                label: "تاريخ الاستحقاق",
              },
            ]}
            shallow={true}
            debounceMs={250}
          />
        </div>

        {/* ترتيب المهام */}
        <div>
          <ToolbarSorting<Task>
            items={[
              { id: "priority", label: "الأولوية" },
              { id: "status", label: "الحالة" },
              { id: "dueDate", label: "تاريخ الاستحقاق" },
              { id: "createdAt", label: "تاريخ الإنشاء" },
            ]}
          />
        </div>
      </Toolbar>

      {/* قائمة المهام */}
      <div className={isPending ? "opacity-50" : ""}>
        <TasksList />
      </div>
    </div>
  )
}
```

### 🎓 مثال 4: نظام إدارة الطلاب

```tsx
interface Student {
  id: string
  name: string
  email: string
  major: string
  year: number
  gpa: number
  isActive: boolean
  enrollmentDate: Date
  courses: string[]
}

function StudentsManagement() {
  const [isPending, startTransition] = useTransition()

  return (
    <div className="space-y-6">
      <div className="bg-card rounded-lg border p-6">
        <h2 className="mb-4 text-2xl font-bold">إدارة الطلاب</h2>

        <Toolbar startTransition={startTransition} className="space-y-4">
          {/* شريط البحث */}
          <div className="flex gap-4">
            <ToolbarSearch<Student>
              item={{
                id: "name",
                placeholder: "بحث بالاسم أو البريد الإلكتروني...",
              }}
              className="flex-1"
            />
          </div>

          {/* الفلاتر والترتيب */}
          <div className="flex flex-wrap gap-4">
            <ToolbarFilter<Student>
              items={[
                {
                  variant: "text",
                  id: "name",
                  label: "اسم الطالب",
                  placeholder: "أدخل الاسم...",
                },
                {
                  variant: "select",
                  id: "major",
                  label: "التخصص",
                  options: [
                    { label: "علوم الحاسوب", value: "cs", count: 45 },
                    { label: "الهندسة", value: "engineering", count: 38 },
                    { label: "الطب", value: "medicine", count: 29 },
                    { label: "الأعمال", value: "business", count: 33 },
                  ],
                },
                {
                  variant: "number",
                  id: "year",
                  label: "السنة الدراسية",
                  placeholder: "1-4",
                },
                {
                  variant: "number",
                  id: "gpa",
                  label: "المعدل التراكمي",
                  placeholder: "0.0-4.0",
                },
                {
                  variant: "boolean",
                  id: "isActive",
                  label: "طالب نشط",
                },
                {
                  variant: "multiSelect",
                  id: "courses",
                  label: "المقررات",
                  options: [
                    { label: "برمجة 1", value: "prog1" },
                    { label: "قواعد البيانات", value: "db" },
                    { label: "شبكات", value: "networks" },
                    { label: "ذكاء اصطناعي", value: "ai" },
                  ],
                },
              ]}
            />

            <ToolbarSorting<Student>
              items={[
                { id: "name", label: "الاسم" },
                { id: "gpa", label: "المعدل" },
                { id: "year", label: "السنة" },
                { id: "enrollmentDate", label: "تاريخ التسجيل" },
              ]}
            />
          </div>
        </Toolbar>
      </div>

      {/* جدول الطلاب */}
      <div className={isPending ? "opacity-50" : ""}>
        <StudentsTable />
      </div>
    </div>
  )
}
```

### 🏢 مثال 5: نظام إدارة الموظفين (مثال متكامل)

```tsx
import { useTransition } from "react"

import { DataTable } from "@/components/ui/data-table"
import {
  Toolbar,
  ToolbarFilter,
  ToolbarSearch,
  ToolbarSorting,
} from "@/components/toolbar"

interface Employee {
  id: string
  name: string
  email: string
  department: string
  position: string
  salary: number
  hireDate: Date
  isActive: boolean
  skills: string[]
  performance: "excellent" | "good" | "average" | "poor"
}

function EmployeeManagement() {
  const [isPending, startTransition] = useTransition()

  return (
    <div className="container mx-auto space-y-6 py-6">
      {/* العنوان */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">إدارة الموظفين</h1>
        <div className="text-muted-foreground text-sm">
          {isPending && "جاري التحديث..."}
        </div>
      </div>

      {/* شريط الأدوات */}
      <div className="bg-card rounded-lg border p-6 shadow-sm">
        <Toolbar
          startTransition={startTransition}
          locale="ar"
          dir="rtl"
          className="space-y-4"
        >
          {/* البحث الرئيسي */}
          <div className="w-full">
            <ToolbarSearch<Employee>
              item={{
                id: "name",
                placeholder: "بحث بالاسم، البريد الإلكتروني، أو المنصب...",
              }}
              debounceMs={300}
              className="w-full max-w-md"
            />
          </div>

          {/* الفلاتر والترتيب */}
          <div className="flex flex-col gap-4 lg:flex-row">
            {/* الفلاتر */}
            <div className="flex-1">
              <ToolbarFilter<Employee>
                items={[
                  {
                    variant: "text",
                    id: "name",
                    label: "اسم الموظف",
                    placeholder: "أدخل الاسم...",
                  },
                  {
                    variant: "select",
                    id: "department",
                    label: "القسم",
                    options: [
                      { label: "تقنية المعلومات", value: "it", count: 25 },
                      { label: "الموارد البشرية", value: "hr", count: 8 },
                      { label: "المبيعات", value: "sales", count: 15 },
                      { label: "التسويق", value: "marketing", count: 12 },
                      { label: "المالية", value: "finance", count: 10 },
                    ],
                  },
                  {
                    variant: "select",
                    id: "position",
                    label: "المنصب",
                    options: [
                      { label: "مطور", value: "developer" },
                      { label: "مدير", value: "manager" },
                      { label: "محلل", value: "analyst" },
                      { label: "مصمم", value: "designer" },
                    ],
                  },
                  {
                    variant: "number",
                    id: "salary",
                    label: "الراتب",
                    placeholder: "أدخل الراتب...",
                  },
                  {
                    variant: "select",
                    id: "performance",
                    label: "تقييم الأداء",
                    options: [
                      { label: "ممتاز", value: "excellent", count: 12 },
                      { label: "جيد", value: "good", count: 28 },
                      { label: "متوسط", value: "average", count: 15 },
                      { label: "ضعيف", value: "poor", count: 3 },
                    ],
                  },
                  {
                    variant: "multiSelect",
                    id: "skills",
                    label: "المهارات",
                    options: [
                      { label: "JavaScript", value: "javascript" },
                      { label: "Python", value: "python" },
                      { label: "React", value: "react" },
                      { label: "Node.js", value: "nodejs" },
                      { label: "SQL", value: "sql" },
                    ],
                  },
                  {
                    variant: "boolean",
                    id: "isActive",
                    label: "موظف نشط",
                  },
                  {
                    variant: "date",
                    id: "hireDate",
                    label: "تاريخ التوظيف",
                  },
                ]}
                debounceMs={400}
                shallow={false}
              />
            </div>

            {/* الترتيب */}
            <div className="lg:w-auto">
              <ToolbarSorting<Employee>
                items={[
                  { id: "name", label: "الاسم" },
                  { id: "department", label: "القسم" },
                  { id: "salary", label: "الراتب" },
                  { id: "performance", label: "الأداء" },
                  { id: "hireDate", label: "تاريخ التوظيف" },
                ]}
              />
            </div>
          </div>
        </Toolbar>
      </div>

      {/* الجدول */}
      <div
        className={`transition-opacity duration-200 ${isPending ? "opacity-50" : ""}`}
      >
        <DataTable
          columns={employeeColumns}
          data={employees}
          className="bg-card rounded-lg border"
        />
      </div>

      {/* إحصائيات */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <div className="bg-card rounded-lg border p-4">
          <h3 className="text-muted-foreground text-sm font-semibold">
            إجمالي الموظفين
          </h3>
          <p className="text-2xl font-bold">58</p>
        </div>
        <div className="bg-card rounded-lg border p-4">
          <h3 className="text-muted-foreground text-sm font-semibold">
            الموظفون النشطون
          </h3>
          <p className="text-2xl font-bold text-green-600">52</p>
        </div>
        <div className="bg-card rounded-lg border p-4">
          <h3 className="text-muted-foreground text-sm font-semibold">
            متوسط الراتب
          </h3>
          <p className="text-2xl font-bold">$65,000</p>
        </div>
        <div className="bg-card rounded-lg border p-4">
          <h3 className="text-muted-foreground text-sm font-semibold">
            الأداء الممتاز
          </h3>
          <p className="text-2xl font-bold text-blue-600">12</p>
        </div>
      </div>
    </div>
  )
}
```

### 💡 نصائح للاستخدام الأمثل

#### 1. **تنظيم المكونات**

```tsx
// ✅ جيد - تنظيم واضح
<Toolbar startTransition={startTransition}>
  <div className="flex gap-4">
    <ToolbarSearch item={{...}} />
    <ToolbarFilter items={[...]} />
    <ToolbarSorting items={[...]} />
  </div>
</Toolbar>

```

#### 2. **إدارة الأداء**

```tsx
// ✅ استخدم debouncing مناسب
<ToolbarFilter
  items={items}
  debounceMs={300}  // للبيانات الصغيرة
/>

<ToolbarFilter
  items={items}
  debounceMs={500}  // للبيانات الكبيرة
/>
```

---

## 🔧 استكشاف الأخطاء والحلول

### ❌ المشاكل الشائعة وحلولها

#### 1. **خطأ: "useToolbarContext must be used within a ToolbarProvider"**

**السبب**: استخدام مكون فرعي خارج `Toolbar`

```tsx
// ❌ خطأ
function MyComponent() {
  return (
    <div>
      <ToolbarSearch item={{ id: "name" }} /> {/* خطأ! */}
    </div>
  )
}

// ✅ الحل
function MyComponent() {
  return (
    <Toolbar>
      <ToolbarSearch item={{ id: "name" }} /> {/* صحيح */}
    </Toolbar>
  )
}
```

#### 2. **المشكلة: الفلاتر لا تعمل أو تختفي**

**الأسباب المحتملة**:

- معرفات مكررة في `items`
- نوع البيانات غير متطابق مع `variant`
- عدم توفير `options` للـ select

```tsx
// ❌ خطأ - معرفات مكررة
<ToolbarFilter
  items={[
    { variant: "text", id: "name", label: "الاسم" },
    { variant: "text", id: "name", label: "اسم آخر" }  // خطأ!
  ]}
/>

// ✅ الحل - معرفات فريدة
<ToolbarFilter
  items={[
    { variant: "text", id: "firstName", label: "الاسم الأول" },
    { variant: "text", id: "lastName", label: "الاسم الأخير" }
  ]}
/>

// ❌ خطأ - select بدون options
<ToolbarFilter
  items={[
    { variant: "select", id: "status", label: "الحالة" }  // خطأ!
  ]}
/>

// ✅ الحل - إضافة options
<ToolbarFilter
  items={[
    {
      variant: "select",
      id: "status",
      label: "الحالة",
      options: [
        { label: "نشط", value: "active" },
        { label: "غير نشط", value: "inactive" }
      ]
    }
  ]}
/>
```

#### 3. **المشكلة: البحث بطيء أو يسبب مشاكل في الأداء**

**الحلول**:

```tsx
// ✅ استخدم debouncing مناسب
<ToolbarSearch
  item={{id: "name"}}
  debounceMs={500}  // زيادة التأخير للبيانات الكبيرة
/>

// ✅ استخدم shallow routing
<ToolbarSearch
  item={{id: "name"}}
  shallow={true}  // تجنب إعادة تحميل الصفحة
/>

// ✅ استخدم React Transitions
const [isPending, startTransition] = useTransition()

<Toolbar startTransition={startTransition}>
  <ToolbarSearch item={{id: "name"}} />
</Toolbar>
```

#### 4. **المشكلة: الترتيب لا يعمل بشكل صحيح**

**الأسباب والحلول**:

```tsx
// ❌ خطأ - معرفات غير صحيحة
interface User {
  id: string
  fullName: string  // الحقل الفعلي
}

<ToolbarSorting<User>
  items={[
    { id: "name", label: "الاسم" }  // خطأ! لا يوجد حقل "name"
  ]}
/>

// ✅ الحل - استخدام الحقول الصحيحة
<ToolbarSorting<User>
  items={[
    { id: "fullName", label: "الاسم" }  // صحيح
  ]}
/>
```

#### 5. **المشكلة: اللغة والاتجاه غير صحيحين**

```tsx
// ❌ خطأ - عدم تطابق اللغة والاتجاه
<Toolbar locale="ar" dir="ltr">  {/* خطأ! */}
  {/* المحتوى */}
</Toolbar>

// ✅ الحل - تطابق صحيح
<Toolbar locale="ar" dir="rtl">  {/* للعربية */}
  {/* المحتوى */}
</Toolbar>

<Toolbar locale="en" dir="ltr">  {/* للإنجليزية */}
  {/* المحتوى */}
</Toolbar>
```

### 🚨 رسائل الخطأ الشائعة

#### "Error From useFilterState"

- **السبب**: استخدام `useFilterState` خارج `FilterContext`
- **الحل**: تأكد من وجود المكون داخل `ToolbarFilter`

#### "Cannot read property 'id' of undefined"

- **السبب**: عدم تمرير `item` أو `items` بشكل صحيح
- **الحل**: تحقق من صحة البيانات المرسلة

#### "Invalid hook call"

- **السبب**: استخدام hooks خارج مكون React
- **الحل**: تأكد من استخدام المكونات داخل مكونات React صحيحة

### 💡 نصائح لتجنب المشاكل

#### 1. **التحقق من الأنواع (TypeScript)**

```tsx
// ✅ استخدم TypeScript للتحقق من الأنواع
interface MyData {
  id: string
  name: string
  age: number
}

;<ToolbarFilter<MyData>
  items={[
    { variant: "text", id: "name", label: "الاسم" }, // TypeScript يتحقق
    { variant: "number", id: "age", label: "العمر" },
  ]}
/>
```

#### 2. **اختبار المكونات**

```tsx
// مثال على اختبار بسيط
import { render, screen } from "@testing-library/react"

import { Toolbar, ToolbarSearch } from "@/components/toolbar"

test("يجب أن يعرض حقل البحث", () => {
  render(
    <Toolbar>
      <ToolbarSearch item={{ id: "name", placeholder: "بحث..." }} />
    </Toolbar>
  )

  expect(screen.getByPlaceholderText("بحث...")).toBeInTheDocument()
})
```

#### 3. **مراقبة الأداء**

```tsx
// استخدم React DevTools Profiler
import { Profiler } from "react"

function onRenderCallback(id, phase, actualDuration) {
  console.log("Toolbar render time:", actualDuration)
}

;<Profiler id="Toolbar" onRender={onRenderCallback}>
  <Toolbar>{/* المكونات */}</Toolbar>
</Profiler>
```

#### 4. **التعامل مع البيانات الكبيرة**

```tsx
// للبيانات الكبيرة، استخدم:
;<ToolbarFilter
  items={items}
  debounceMs={800} // تأخير أطول
  shallow={true} // shallow routing
/>

// وفي المكون الرئيسي:
const [isPending, startTransition] = useTransition()

// عرض مؤشر التحميل
{
  isPending && <LoadingSpinner />
}
```

### 🔍 أدوات التشخيص

#### 1. **فحص URL**

```javascript
// في console المتصفح
console.log(window.location.search)
// يجب أن تظهر معاملات مثل: ?filters=[...]&sort=[...]&search={...}
```

#### 2. **فحص حالة المكونات**

```tsx
// إضافة logging للتشخيص
<ToolbarFilter
  items={items}
  onChange={(filters) => {
    console.log("Current filters:", filters)
  }}
/>
```

#### 3. **استخدام React DevTools**

- فحص `ToolbarContext` للتأكد من القيم
- مراقبة re-renders غير الضرورية
- فحص props المرسلة للمكونات

---

## 📚 مراجع إضافية

### 🔗 روابط مفيدة

- **مكتبة nuqs**: [https://nuqs.47ng.com/](https://nuqs.47ng.com/) - لإدارة حالة URL
- **مكتبة zod**: [https://zod.dev/](https://zod.dev/) - للتحقق من صحة البيانات
- **React Transitions**: [https://react.dev/reference/react/useTransition](https://react.dev/reference/react/useTransition)
- **Shadcn/ui**: [https://ui.shadcn.com/](https://ui.shadcn.com/) - مكتبة المكونات المستخدمة

### 📖 مصطلحات مهمة

| المصطلح             | الترجمة        | الوصف                                            |
| ------------------- | -------------- | ------------------------------------------------ |
| Debouncing          | التأخير الذكي  | تأخير تنفيذ العملية حتى توقف المستخدم عن الكتابة |
| Shallow Routing     | التوجيه السطحي | تحديث URL بدون إعادة تحميل الصفحة                |
| Context             | السياق         | نظام React لمشاركة البيانات بين المكونات         |
| State Management    | إدارة الحالة   | تتبع وتحديث بيانات التطبيق                       |
| TypeScript Generics | الأنواع العامة | نظام لجعل المكونات تعمل مع أنواع بيانات مختلفة   |

### 🎯 أفضل الممارسات

#### 1. **تنظيم الكود**

```tsx
// ✅ تنظيم جيد
const filterItems = [
  { variant: "text", id: "name", label: "الاسم" },
  { variant: "select", id: "status", label: "الحالة", options: statusOptions },
]

const sortingItems = [
  { id: "name", label: "الاسم" },
  { id: "createdAt", label: "تاريخ الإنشاء" },
]

return (
  <Toolbar startTransition={startTransition}>
    <ToolbarSearch item={searchItem} />
    <ToolbarFilter items={filterItems} />
    <ToolbarSorting items={sortingItems} />
  </Toolbar>
)
```

#### 2. **إدارة الحالة**

```tsx
// ✅ استخدام useTransition للأداء
const [isPending, startTransition] = useTransition()

// ✅ عرض حالة التحميل
<div className={isPending ? "opacity-50" : ""}>
  <DataTable />
</div>
```

#### 3. **التجاوب والتصميم**

```tsx
// ✅ تصميم متجاوب
<Toolbar className="flex flex-col gap-4 p-4 md:flex-row md:items-center lg:gap-6">
  <ToolbarSearch className="w-full md:w-auto md:flex-1" />
  <div className="flex gap-2">
    <ToolbarFilter />
    <ToolbarSorting />
  </div>
</Toolbar>
```

### 🚀 خطوات البدء السريع

#### 1. **التثبيت الأساسي**

```bash
# تأكد من وجود المكتبات المطلوبة
npm install nuqs zod lucide-react
```

#### 2. **الإعداد الأولي**

```tsx
// في صفحتك الرئيسية
import { useTransition } from "react"

import { Toolbar, ToolbarSearch } from "@/components/toolbar"

function MyPage() {
  const [isPending, startTransition] = useTransition()

  return (
    <div>
      <Toolbar startTransition={startTransition}>
        <ToolbarSearch item={{ id: "name", placeholder: "بحث..." }} />
      </Toolbar>

      <div className={isPending ? "opacity-50" : ""}>{/* محتوى الصفحة */}</div>
    </div>
  )
}
```

#### 3. **إضافة المزيد من الميزات**

```tsx
// أضف الفلاتر والترتيب تدريجياً
<Toolbar startTransition={startTransition}>
  <ToolbarSearch item={{ id: "name", placeholder: "بحث..." }} />

  <ToolbarFilter items={[{ variant: "text", id: "name", label: "الاسم" }]} />

  <ToolbarSorting items={[{ id: "name", label: "الاسم" }]} />
</Toolbar>
```

---

## 🎉 الخاتمة

تهانينا! لقد تعلمت كيفية استخدام مكونات **Toolbar** بشكل شامل. هذه المكونات توفر:

### ✅ ما تعلمته

- **المكون الرئيسي Toolbar**: الحاوي الأساسي الذي يوفر السياق
- **مكون البحث ToolbarSearch**: بحث فوري مع تأخير ذكي
- **مكون التصفية ToolbarFilter**: فلاتر متقدمة لجميع أنواع البيانات
- **مكون الترتيب ToolbarSorting**: ترتيب متعدد المستويات مع السحب والإفلات

### 🚀 الخطوات التالية

1. **جرب الأمثلة**: ابدأ بالأمثلة البسيطة ثم انتقل للمعقدة
2. **خصص التصميم**: استخدم CSS classes لتخصيص الشكل
3. **اختبر الأداء**: راقب الأداء مع البيانات الكبيرة
4. **شارك التجربة**: استخدم URL sharing لمشاركة الحالة

### 💡 نصيحة أخيرة

ابدأ بسيط ثم أضف الميزات تدريجياً. هذه المكونات مصممة لتكون مرنة وقابلة للتوسع، لذا لا تتردد في تجربة تركيبات مختلفة!

---

**تم إنشاء هذا التوثيق بحب ❤️ للمطورين العرب**

_آخر تحديث: يوليو 2025_
